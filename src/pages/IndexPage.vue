<template>
  <q-page class="q-pa-md">
    <div class="q-pb-md">
      <div>
        <q-btn label="Add Source" @click="addSource" color="primary" class="q-ml-auto" />
        <q-btn label="Generate AMCP/HTML" @click="generate" color="secondary" class="q-ml-auto" />
        <q-btn
          label="Send AMCP to Caspar"
          @click="generate"
          color="secondary"
          class="q-ml-auto"
          disable
        />
      </div>
      <div>
        <q-btn label="Save Layout" @click="saveLayout" color="secondary" class="q-ml-auto" />
        <q-btn label="Load Layout" @click="loadLayout" color="secondary" class="q-ml-auto" />
      </div>
    </div>
    <div class="canvas-section q-mb-md">
      <div ref="container" class="konva-container" />
    </div>

    <div class="tiles-panel-section">
      <q-card class="tiles-panel">
        <q-card-section>
          <div class="text-h6">Tiles</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <div v-if="tiles.length === 0" class="text-grey-6 text-center q-pa-md">
            No tiles added yet. Click "Add Source" to create your first tile.
          </div>
          <div v-else class="tiles-list">
            <q-card
              v-for="tile in tiles"
              :key="tile.id"
              class="tile-item q-mb-sm"
              flat
              bordered
              @click="selectTile(tile.id)"
              :class="{ 'tile-selected': selectedTileId === tile.id }"
            >
              <q-card-section class="q-pa-sm">
                <div class="row items-center">
                  <div class="col">
                    <div class="text-subtitle2">{{ tile.label }}</div>
                    <div class="text-caption text-grey-6">{{ tile.source }}</div>
                    <div class="text-caption text-grey-5">
                      {{ Math.round(tile.x) }}, {{ Math.round(tile.y) }} •
                      {{ Math.round(tile.width) }}×{{ Math.round(tile.height) }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <q-btn
                      flat
                      round
                      dense
                      icon="edit"
                      size="sm"
                      @click.stop="editTileFromPanel(tile.id)"
                      class="q-mr-xs"
                    />
                    <q-btn
                      flat
                      round
                      dense
                      icon="delete"
                      size="sm"
                      color="negative"
                      @click.stop="deleteTileFromPanel(tile.id)"
                    />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Context Menu -->
    <div
      v-show="menuVisible"
      class="context-menu"
      :style="{ top: menuY + 'px', left: menuX + 'px' }"
      @click.stop
    >
      <button @click.stop="openEditDialog">Edit Properties</button>
      <button @click.stop="deleteTile">Delete</button>
    </div>

    <!-- Persistent Quasar Dialog for Editing -->
    <q-dialog v-model="dialogVisible" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Edit Source: {{ editTileId }}</div>
        </q-card-section>
        <q-card-section>
          <q-input v-model="editLabel" label="Label" autofocus />
          <q-input v-model="editSource" label="NDI Source" class="q-mt-sm" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" v-close-popup />
          <q-btn flat label="Apply" color="primary" @click="applyEdits" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import Konva from 'konva'

// Canvas dimensions
const canvasWidth = 1920
const canvasHeight = 1080

// Reactive state
const tiles = ref([])
const container = ref(null)
let stage, layer, transformer, gridLayer

// Context menu state
const menuVisible = ref(false)
const menuX = ref(0)
const menuY = ref(0)
let currentTileId = null

// Dialog state
const dialogVisible = ref(false)
const editTileId = ref('')
const editLabel = ref('')
const editSource = ref('')

// Panel state
const selectedTileId = ref(null)

// Add new source tile
function addSource() {
  const idx = tiles.value.length + 1
  const id = `cam${idx}`

  // Create tile with 16:9 aspect ratio (1/4 of canvas width)
  const tileWidth = canvasWidth / 4
  const tileHeight = tileWidth / (16 / 9) // Maintain 16:9 aspect ratio

  tiles.value.push({
    id,
    label: `CAM ${idx}`,
    source: `ndi://Camera${idx}`,
    x: 20,
    y: 20,
    width: tileWidth,
    height: tileHeight,
  })
  nextTick(() => createGroupFor(tiles.value.find((t) => t.id === id)))
}

// Snap helper (grid size = 20)
function snap(value, grid = 20) {
  return Math.round(value / grid) * grid
}

// Create grid overlay
function createGrid() {
  const gridSize = 20
  const group = new Konva.Group({ name: 'grid', visible: false })

  // Vertical lines
  for (let x = 0; x <= canvasWidth; x += gridSize) {
    const line = new Konva.Line({
      points: [x, 0, x, canvasHeight],
      stroke: 'rgba(255, 255, 255, 0.3)',
      strokeWidth: 1,
    })
    group.add(line)
  }

  // Horizontal lines
  for (let y = 0; y <= canvasHeight; y += gridSize) {
    const line = new Konva.Line({
      points: [0, y, canvasWidth, y],
      stroke: 'rgba(255, 255, 255, 0.3)',
      strokeWidth: 1,
    })
    group.add(line)
  }

  return group
}

// Show/hide grid
function showGrid() {
  const grid = gridLayer.findOne('.grid')
  if (grid) grid.visible(true)
  gridLayer.batchDraw()
}

function hideGrid() {
  const grid = gridLayer.findOne('.grid')
  if (grid) grid.visible(false)
  gridLayer.batchDraw()
}

// Boundary checking functions
function constrainToCanvas(x, y, width, height) {
  const constrainedX = Math.max(0, Math.min(x, canvasWidth - width))
  const constrainedY = Math.max(0, Math.min(y, canvasHeight - height))
  return { x: constrainedX, y: constrainedY }
}

// Enforce 16:9 aspect ratio
function enforceAspectRatio(width, height) {
  const aspectRatio = 16 / 9
  const currentRatio = width / height

  if (currentRatio > aspectRatio) {
    // Too wide, adjust width
    width = height * aspectRatio
  } else if (currentRatio < aspectRatio) {
    // Too tall, adjust height
    height = width / aspectRatio
  }

  return { width, height }
}

function constrainSize(width, height, x, y) {
  const maxWidth = canvasWidth - x
  const maxHeight = canvasHeight - y

  // First enforce 16:9 aspect ratio
  const aspectConstrained = enforceAspectRatio(width, height)

  // Then constrain to canvas boundaries with minimum size
  const constrainedWidth = Math.max(32, Math.min(aspectConstrained.width, maxWidth)) // Minimum 32px for 16:9
  const constrainedHeight = Math.max(18, Math.min(aspectConstrained.height, maxHeight)) // Minimum 18px for 16:9

  // Final aspect ratio check after boundary constraints
  const finalConstrained = enforceAspectRatio(constrainedWidth, constrainedHeight)

  return { width: finalConstrained.width, height: finalConstrained.height }
}

// Create Konva group for a tile
function createGroupFor(tile) {
  const grp = new Konva.Group({ name: tile.id, x: tile.x, y: tile.y, draggable: true })

  const rect = new Konva.Rect({
    name: 'rect',
    width: tile.width,
    height: tile.height,
    stroke: 'white',
    strokeWidth: 2,
    fill: 'rgba(0,0,0,0.3)',
  })

  const labelText = new Konva.Text({
    name: 'labelText',
    text: tile.label,
    fontSize: 14,
    padding: 4,
    fill: 'black',
    width: tile.width,
    align: 'center',
  })

  const label = new Konva.Label({ name: 'label', y: tile.height - 23 })
  const tag = new Konva.Tag({
    name: 'tag',
    fill: 'white',
    width: tile.width,
    height: labelText.height(),
  })
  label.add(tag)
  label.add(labelText)

  grp.add(rect)
  grp.add(label)
  layer.add(grp)

  // Select on click
  grp.on('click', () => {
    transformer.nodes([grp])
    selectedTileId.value = tile.id
    layer.draw()
  })

  // Context menu on right-click
  grp.on('contextmenu', (e) => {
    e.evt.preventDefault()
    currentTileId = tile.id
    const pos = stage.getPointerPosition()
    const bb = stage.container().getBoundingClientRect()
    menuX.value = bb.left + pos.x
    menuY.value = bb.top + pos.y
    nextTick(() => (menuVisible.value = true))
  })

  // Show grid on drag start
  grp.on('dragstart', () => {
    showGrid()
  })

  // Snap drag end with boundary constraints
  grp.on('dragend', () => {
    hideGrid()
    const rawX = grp.x()
    const rawY = grp.y()
    const snappedX = snap(rawX)
    const snappedY = snap(rawY)

    // Constrain to canvas boundaries
    const constrained = constrainToCanvas(snappedX, snappedY, tile.width, tile.height)

    grp.position(constrained)
    tile.x = constrained.x
    tile.y = constrained.y
    layer.draw()
  })

  // Live boundary checking during drag
  grp.on('dragmove', () => {
    const rawX = grp.x()
    const rawY = grp.y()
    const constrained = constrainToCanvas(rawX, rawY, tile.width, tile.height)

    if (rawX !== constrained.x || rawY !== constrained.y) {
      grp.position(constrained)
    }
  })

  // Show grid on transform start
  grp.on('transformstart', () => {
    showGrid()
  })

  // Snap resize end with boundary constraints
  grp.on('transformend', () => {
    hideGrid()
    const scaleX = grp.scaleX()
    const scaleY = grp.scaleY()

    // Calculate new dimensions
    let newWidth = snap(rect.width() * scaleX)
    let newHeight = snap(rect.height() * scaleY)

    // Constrain size to canvas boundaries
    const constrained = constrainSize(newWidth, newHeight, grp.x(), grp.y())
    tile.width = constrained.width
    tile.height = constrained.height

    // Reset scale and apply new dimensions
    grp.scale({ x: 1, y: 1 })
    rect.width(tile.width)
    rect.height(tile.height)
    label.y(tile.height - 23)
    labelText.width(tile.width)
    tag.width(tile.width)

    // Update tile position
    tile.x = grp.x()
    tile.y = grp.y()
    layer.draw()
  })
}

// Initialize Konva stage, layer, transformer
onMounted(() => {
  stage = new Konva.Stage({ container: container.value, width: canvasWidth, height: canvasHeight })

  // Create grid layer (behind everything)
  gridLayer = new Konva.Layer()
  const grid = createGrid()
  gridLayer.add(grid)
  stage.add(gridLayer)

  // Create main layer
  layer = new Konva.Layer()
  stage.add(layer)

  transformer = new Konva.Transformer({
    rotateEnabled: false,
    enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
    boundBoxFunc: (_, newBox) => {
      // Enforce 16:9 aspect ratio first
      const aspectConstrained = enforceAspectRatio(newBox.width, newBox.height)

      // Constrain transformer to canvas boundaries
      const constrainedX = Math.max(0, Math.min(newBox.x, canvasWidth - aspectConstrained.width))
      const constrainedY = Math.max(0, Math.min(newBox.y, canvasHeight - aspectConstrained.height))
      const constrainedWidth = Math.min(aspectConstrained.width, canvasWidth - constrainedX)
      const constrainedHeight = Math.min(aspectConstrained.height, canvasHeight - constrainedY)

      // Final aspect ratio enforcement after boundary constraints
      const finalConstrained = enforceAspectRatio(
        Math.max(32, constrainedWidth), // Minimum 32px width for 16:9
        Math.max(18, constrainedHeight), // Minimum 18px height for 16:9
      )

      return {
        x: constrainedX,
        y: constrainedY,
        width: finalConstrained.width,
        height: finalConstrained.height,
        rotation: newBox.rotation,
      }
    },
  })
  layer.add(transformer)

  // Deselect on empty click
  stage.on('click', (e) => {
    if (e.target === stage) {
      transformer.nodes([])
      selectedTileId.value = null
      layer.draw()
    }
    menuVisible.value = false
  })

  // Initial render of any loaded tiles
  tiles.value.forEach(createGroupFor)
})

onBeforeUnmount(() => stage.destroy())

// Context menu actions
function openEditDialog() {
  const tile = tiles.value.find((t) => t.id === currentTileId)
  if (!tile) return
  editTileId.value = tile.id
  editLabel.value = tile.label
  editSource.value = tile.source
  dialogVisible.value = true
  menuVisible.value = false
}

function deleteTile() {
  tiles.value = tiles.value.filter((t) => t.id !== currentTileId)
  const grp = layer.findOne((node) => node.getName() === currentTileId)
  if (grp) grp.destroy()
  transformer.nodes([])
  selectedTileId.value = null
  menuVisible.value = false
  layer.draw()
}

function applyEdits() {
  const tile = tiles.value.find((t) => t.id === editTileId.value)
  if (tile) {
    tile.label = editLabel.value
    tile.source = editSource.value
    const grp = layer.findOne((n) => n.getName() === tile.id)
    const txt = grp?.findOne((n) => n.getName() === 'labelText')
    txt?.text(tile.label)

    // Update label tag width to match new text
    const label = grp?.findOne((n) => n.getName() === 'label')
    const tag = label?.findOne((n) => n.getName() === 'tag')
    if (tag) tag.width(tile.width)

    layer.draw()
  }
  dialogVisible.value = false
}

// Panel functions
function selectTile(tileId) {
  selectedTileId.value = tileId
  const grp = layer.findOne((node) => node.getName() === tileId)
  if (grp) {
    transformer.nodes([grp])
    layer.draw()
  }
}

function editTileFromPanel(tileId) {
  const tile = tiles.value.find((t) => t.id === tileId)
  if (!tile) return
  editTileId.value = tile.id
  editLabel.value = tile.label
  editSource.value = tile.source
  dialogVisible.value = true
  selectedTileId.value = tileId
}

function deleteTileFromPanel(tileId) {
  tiles.value = tiles.value.filter((t) => t.id !== tileId)
  const grp = layer.findOne((node) => node.getName() === tileId)
  if (grp) grp.destroy()
  transformer.nodes([])
  selectedTileId.value = null
  layer.draw()
}

// Save layout - serializes plain JSON
async function saveLayout() {
  const data = JSON.parse(JSON.stringify(tiles.value))
  try {
    await window.api.saveLayout(data)
    console.log('Layout saved')
  } catch (err) {
    console.error('Save failed:', err)
  }
}

// Load layout - replaces tiles and re-renders
async function loadLayout() {
  const loaded = await window.api.loadLayout()
  if (loaded) {
    // Expect loaded.{tiles: [...]}
    const arr = Array.isArray(loaded.tiles) ? loaded.tiles : loaded
    tiles.value = arr
    // Clear existing groups and re-add transformer
    layer.clear() // removes children
    layer.add(transformer)
    // Re-create groups
    tiles.value.forEach(createGroupFor)
    layer.batchDraw()
  }
}

// Generate AMCP + HTML overlay
function generate() {
  const cmds = [],
    htmlParts = []
  tiles.value.forEach((t, i) => {
    const layerIdx = 10 + i
    const fx = (t.x / canvasWidth).toFixed(4)
    const fy = (t.y / canvasHeight).toFixed(4)
    const fw = (t.width / canvasWidth).toFixed(4)
    const fh = (t.height / canvasHeight).toFixed(4)
    cmds.push(
      `PLAY 1-${layerIdx} "${t.source}"`,
      `MIXER 1-${layerIdx} FILL ${fx} ${fy} ${fw} ${fh}`,
      `CG 1-${layerIdx + 100} ADD 1 label-template 1 '{"text":"${t.label}"}'`,
      `MIXER 1-${layerIdx + 100} FILL ${fx} ${fy} ${fw} 0.05`,
    )
    htmlParts.push(`
      <div class="label" style="left:${fx * 100}%;top:${fy * 100}%;width:${fw * 100}%;height:5%;">${t.label}</div>`)
  })
  console.log('–– AMCP Commands ––\n' + cmds.join('\n'))
  console.log('–– Overlay HTML ––\n' + htmlParts)
}
</script>

<style scoped>
.canvas-section {
  display: flex;
  justify-content: center;
  width: 100%;
}

.konva-container {
  width: 1920px;
  height: 1080px;
  border: 3px solid #ff0000;
  background: #222;
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}

.tiles-panel-section {
  width: 100%;
}

.tiles-panel {
  max-height: 400px;
  overflow-y: auto;
}

.tiles-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.tile-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.tile-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.tile-selected {
  background-color: rgba(25, 118, 210, 0.1);
  border-color: #1976d2 !important;
}

.context-menu {
  position: fixed;
  background: #ffffff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 4px 0;
}

.context-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
}

.context-menu button:hover {
  background: #eee;
}
</style>
