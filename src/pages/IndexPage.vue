<template>
  <q-page class="q-pa-md">
    <div class="q-pb-md">
      <div>
        <q-btn label="Add Source" @click="addSource" color="primary" class="q-ml-auto" />
        <q-btn label="Generate AMCP/HTML" @click="generate" color="secondary" class="q-ml-auto" />
        <q-btn
          label="Send AMCP to Caspar"
          @click="generate"
          color="secondary"
          class="q-ml-auto"
          disable
        />
      </div>
      <div>
        <q-btn label="Save Layout" @click="saveLayout" color="secondary" class="q-ml-auto" />
        <q-btn label="Load Layout" @click="loadLayout" color="secondary" class="q-ml-auto" />
      </div>
    </div>
    <div class="row q-col-gutter-md">
      <div class="col">
        <div ref="container" class="konva-container" />
      </div>
    </div>

    <!-- Context Menu -->
    <div
      v-show="menuVisible"
      class="context-menu"
      :style="{ top: menuY + 'px', left: menuX + 'px' }"
      @click.stop
    >
      <button @click.stop="openEditDialog">Edit Properties</button>
      <button @click.stop="deleteTile">Delete</button>
    </div>

    <!-- Persistent Quasar Dialog for Editing -->
    <q-dialog v-model="dialogVisible" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Edit Source: {{ editTileId }}</div>
        </q-card-section>
        <q-card-section>
          <q-input v-model="editLabel" label="Label" autofocus />
          <q-input v-model="editSource" label="NDI Source" class="q-mt-sm" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" v-close-popup />
          <q-btn flat label="Apply" color="primary" @click="applyEdits" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import Konva from 'konva'

// Canvas dimensions
const canvasWidth = 1280
const canvasHeight = 720

// Reactive state
const tiles = ref([])
const container = ref(null)
let stage, layer, transformer

// Context menu state
const menuVisible = ref(false)
const menuX = ref(0)
const menuY = ref(0)
let currentTileId = null

// Dialog state
const dialogVisible = ref(false)
const editTileId = ref('')
const editLabel = ref('')
const editSource = ref('')

// Add new source tile
function addSource() {
  const idx = tiles.value.length + 1
  const id = `cam${idx}`
  tiles.value.push({
    id,
    label: `CAM ${idx}`,
    source: `ndi://Camera${idx}`,
    x: 20,
    y: 20,
    width: canvasWidth / 4,
    height: canvasHeight / 4,
  })
  nextTick(() => createGroupFor(tiles.value.find((t) => t.id === id)))
}

// Snap helper (grid size = 20)
function snap(value, grid = 20) {
  return Math.round(value / grid) * grid
}

// Clear existing layer content (preserving transformer)
function clearLayer() {
  layer.destroyChildren()
  layer.add(transformer)
}

// Create Konva group for a tile
function createGroupFor(tile) {
  const grp = new Konva.Group({ name: tile.id, x: tile.x, y: tile.y, draggable: true })

  const rect = new Konva.Rect({
    name: 'rect',
    width: tile.width,
    height: tile.height,
    stroke: 'white',
    strokeWidth: 2,
    fill: 'rgba(0,0,0,0.3)',
  })

  const labelText = new Konva.Text({
    name: 'labelText',
    text: tile.label,
    fontSize: 14,
    padding: 4,
    fill: 'black',
    width: tile.width,
    align: 'center',
  })

  const label = new Konva.Label({ name: 'label', y: tile.height - 23 })
  const tag = new Konva.Tag({
    name: 'tag',
    fill: 'white',
    width: tile.width,
    height: labelText.height(),
  })
  label.add(tag)
  label.add(labelText)

  grp.add(rect)
  grp.add(label)
  layer.add(grp)

  // Select on click
  grp.on('click', () => {
    transformer.nodes([grp])
    layer.draw()
  })

  // Context menu on right-click
  grp.on('contextmenu', (e) => {
    e.evt.preventDefault()
    currentTileId = tile.id
    const pos = stage.getPointerPosition()
    const bb = stage.container().getBoundingClientRect()
    menuX.value = bb.left + pos.x
    menuY.value = bb.top + pos.y
    nextTick(() => (menuVisible.value = true))
  })

  // Snap drag end
  grp.on('dragend', () => {
    const x = snap(grp.x())
    const y = snap(grp.y())
    grp.position({ x, y })
    tile.x = x
    tile.y = y
    layer.draw()
  })

  // Snap resize end
  grp.on('transformend', () => {
    const scaleX = grp.scaleX(),
      scaleY = grp.scaleY()
    tile.width = snap(rect.width() * scaleX)
    tile.height = snap(rect.height() * scaleY)
    grp.scale({ x: 1, y: 1 })
    rect.width(tile.width)
    rect.height(tile.height)
    label.y(tile.height - 23)
    labelText.width(tile.width)
    tag.width(tile.width)
    tile.x = grp.x()
    tile.y = grp.y()
    layer.draw()
  })
}

// Initialize Konva stage, layer, transformer
onMounted(() => {
  stage = new Konva.Stage({ container: container.value, width: canvasWidth, height: canvasHeight })
  layer = new Konva.Layer()
  stage.add(layer)
  transformer = new Konva.Transformer({
    rotateEnabled: false,
    enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right'],
  })
  layer.add(transformer)
  // Deselect on empty click
  stage.on('click', (e) => {
    if (e.target === stage) {
      transformer.nodes([])
      layer.draw()
    }
    menuVisible.value = false
  })
  // Initial render of any loaded tiles
  tiles.value.forEach(createGroupFor)
})

onBeforeUnmount(() => stage.destroy())

// Context menu actions
function openEditDialog() {
  const tile = tiles.value.find((t) => t.id === currentTileId)
  if (!tile) return
  editTileId.value = tile.id
  editLabel.value = tile.label
  editSource.value = tile.source
  dialogVisible.value = true
  menuVisible.value = false
}

function deleteTile() {
  tiles.value = tiles.value.filter((t) => t.id !== currentTileId)
  const grp = layer.findOne((node) => node.getName() === currentTileId)
  if (grp) grp.destroy()
  transformer.nodes([])
  menuVisible.value = false
  layer.draw()
}

function applyEdits() {
  const tile = tiles.value.find((t) => t.id === editTileId.value)
  if (tile) {
    tile.label = editLabel.value
    tile.source = editSource.value
    const grp = layer.findOne((n) => n.getName() === tile.id)
    const txt = grp?.findOne((n) => n.getName() === 'labelText')
    txt?.text(tile.label)
    layer.draw()
  }
  dialogVisible.value = false
}

// Save layout - serializes plain JSON
function saveLayout() {
  const data = JSON.parse(JSON.stringify(tiles.value))
  window.api.saveLayout(data, (err) => {
    if (err) console.error('Save failed:', err)
    else console.log('Layout saved')
  })
}

// Load layout - replaces tiles and re-renders
async function loadLayout() {
  const loaded = await window.api.loadLayout()
  if (loaded) {
    tiles.value = loaded
    clearLayer()
    tiles.value.forEach(createGroupFor)
  }
}

// Generate AMCP + HTML overlay
function generate() {
  const cmds = [],
    htmlParts = []
  tiles.value.forEach((t, i) => {
    const layerIdx = 10 + i
    const fx = (t.x / canvasWidth).toFixed(4)
    const fy = (t.y / canvasHeight).toFixed(4)
    const fw = (t.width / canvasWidth).toFixed(4)
    const fh = (t.height / canvasHeight).toFixed(4)
    cmds.push(
      `PLAY 1-${layerIdx} "${t.source}"`,
      `MIXER 1-${layerIdx} FILL ${fx} ${fy} ${fw} ${fh}`,
      `CG 1-${layerIdx + 100} ADD 1 label-template 1 '{"text":"${t.label}"}'`,
      `MIXER 1-${layerIdx + 100} FILL ${fx} ${fy} ${fw} 0.05`,
    )
    htmlParts.push(`
      <div class="label" style="left:${fx * 100}%;top:${fy * 100}%;width:${fw * 100}%;height:5%;">${t.label}</div>`)
  })
  console.log('–– AMCP Commands ––\n' + cmds.join('\n'))
  console.log('–– Overlay HTML ––\n' + htmlParts)
}
</script>

<style scoped>
.konva-container {
  width: 1280px;
  height: 720px;
  border: 3px solid #ff0000;
  background: #222;
}
.context-menu {
  position: fixed;
  background: #ffffff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 4px 0;
}
.context-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
}
.context-menu button:hover {
  background: #eee;
}
</style>
