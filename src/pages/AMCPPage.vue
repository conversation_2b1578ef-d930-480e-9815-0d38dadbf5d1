<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">Generated AMCP Commands</div>

    <div v-if="!generatedData" class="text-center q-pa-xl">
      <q-icon name="code" size="4rem" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-6">No commands generated yet</div>
      <div class="text-body2 text-grey-5">
        Go to the main page and click "Generate AMCP/HTML" to create commands
      </div>
    </div>

    <div v-else>
      <div class="row q-col-gutter-md q-mb-md">
        <div class="col-12 col-md-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">Generation Info</div>
            </q-card-section>
            <q-card-section class="q-pt-none">
              <div class="text-body2">
                <strong>Generated:</strong> {{ generatedData.timestamp }}<br />
                <strong>Tile Count:</strong> {{ generatedData.tileCount }}<br />
                <strong>Commands:</strong> {{ generatedData.commands.length }}
              </div>
            </q-card-section>
          </q-card>
        </div>
        <div class="col-12 col-md-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">Actions</div>
            </q-card-section>
            <q-card-section class="q-pt-none">
              <q-btn
                label="Copy AMCP Commands"
                icon="content_copy"
                color="primary"
                @click="copyCommands"
                class="q-mr-sm q-mb-sm"
              />

              <q-btn
                label="Copy HTML Overlay"
                icon="content_copy"
                color="secondary"
                @click="copyHTML"
                class="q-mr-sm q-mb-sm"
              />

              <q-btn
                label="Download Commands"
                icon="download"
                color="accent"
                @click="downloadCommands"
                class="q-mr-sm q-mb-sm"
              />

              <q-btn
                label="Send AMCP to Caspar"
                icon="send"
                color="secondary"
                @click="sendAMCP"
                class="q-mr-sm q-mb-sm"
                disabled
              />
            </q-card-section>
          </q-card>
        </div>
      </div>

      <div class="row q-col-gutter-md">
        <div class="col-12 col-lg-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">AMCP Commands</div>
            </q-card-section>
            <q-card-section class="q-pt-none">
              <q-input
                v-model="commandsText"
                type="textarea"
                readonly
                outlined
                rows="20"
                class="amcp-commands"
              />
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-lg-6">
          <q-card>
            <q-card-section>
              <div class="text-h6">HTML Overlay</div>
            </q-card-section>
            <q-card-section class="q-pt-none">
              <q-input
                v-model="htmlText"
                type="textarea"
                readonly
                outlined
                rows="20"
                class="html-overlay"
              />
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()
const generatedData = ref(null)

const commandsText = computed(() => {
  if (!generatedData.value) return ''
  return generatedData.value.commands.join('\n')
})

const htmlText = computed(() => {
  if (!generatedData.value) return ''
  const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Multiviewer Overlay</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1920px;
            height: 1080px;
            position: relative;
            background: transparent;
            overflow: hidden;
        }
        .tile-label {
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
${generatedData.value.html.join('\n')}
</body>
</html>`
  return htmlContent
})

onMounted(() => {
  loadGeneratedData()

  // Listen for storage changes to update when new commands are generated
  window.addEventListener('storage', loadGeneratedData)
})

function loadGeneratedData() {
  const stored = localStorage.getItem('generatedAMCP')
  if (stored) {
    try {
      generatedData.value = JSON.parse(stored)
    } catch (e) {
      console.error('Failed to parse stored AMCP data:', e)
    }
  }
}

async function copyCommands() {
  try {
    await navigator.clipboard.writeText(commandsText.value)
    console.log('Commands copied to clipboard')
    $q.notify({
      type: 'positive',
      message: 'AMCP commands copied to clipboard',
    })
  } catch (error) {
    console.error('Failed to copy commands:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to copy commands',
      caption: error.message,
    })
  }
}

async function copyHTML() {
  try {
    await navigator.clipboard.writeText(htmlText.value)
    console.log('HTML copied to clipboard')
    $q.notify({
      type: 'positive',
      message: 'HTML overlay copied to clipboard',
    })
  } catch (error) {
    console.error('Failed to copy HTML:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to copy HTML',
      caption: error.message,
    })
  }
}

function downloadCommands() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  // Download AMCP commands
  const commandsBlob = new Blob([commandsText.value], { type: 'text/plain' })
  const commandsUrl = URL.createObjectURL(commandsBlob)
  const commandsLink = document.createElement('a')
  commandsLink.href = commandsUrl
  commandsLink.download = `amcp-commands-${timestamp}.txt`
  commandsLink.click()
  URL.revokeObjectURL(commandsUrl)

  // Download HTML overlay
  const htmlBlob = new Blob([htmlText.value], { type: 'text/html' })
  const htmlUrl = URL.createObjectURL(htmlBlob)
  const htmlLink = document.createElement('a')
  htmlLink.href = htmlUrl
  htmlLink.download = `overlay-${timestamp}.html`
  htmlLink.click()
  URL.revokeObjectURL(htmlUrl)

  $q.notify({
    type: 'positive',
    message: 'Files downloaded successfully',
  })
}

function sendAMCP() {
  console.log('NO CONFIG:Send AMCP commands to Caspar')
  $q.notify({
    type: 'negative',
    message: 'NO CONFIG: Send AMCP commands to Caspar',
  })
}
</script>

<style scoped>
.amcp-commands,
.html-overlay {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
