<template>
  <q-page class="overlay-editor-page">
    <div class="q-pa-md">
      <div class="text-h4 q-mb-md">HTML Overlay Editor</div>

      <div class="row q-gutter-sm q-mb-md">
        <q-btn label="Import Tiles" @click="importTiles" color="primary" icon="download" />
        <q-btn label="Add Text Box" @click="addTextBox" color="secondary" icon="text_fields" />
        <q-btn label="Preview Overlay" @click="previewOverlay" color="accent" icon="preview" />
        <q-btn label="Export HTML" @click="exportHTML" color="positive" icon="file_download" />
        <q-btn label="Clear All" @click="clearAll" color="negative" outline icon="clear" />
      </div>
    </div>

    <div class="editor-container">
      <div class="canvas-section">
        <div class="overlay-canvas" ref="overlayCanvas">
          <!-- Tile Labels -->
          <div
            v-for="tile in tileLabels"
            :key="tile.id"
            class="overlay-element tile-label"
            :class="{ selected: selectedElement === tile.id }"
            :style="getTileStyle(tile)"
            @click="selectElement(tile.id)"
            @mousedown="startDrag($event, tile.id)"
          >
            {{ tile.text }}
          </div>

          <!-- Text Boxes -->
          <div
            v-for="textBox in textBoxes"
            :key="textBox.id"
            class="overlay-element text-box"
            :class="{ selected: selectedElement === textBox.id }"
            :style="getTextBoxStyle(textBox)"
            @click="selectElement(textBox.id)"
            @mousedown="startDrag($event, textBox.id)"
          >
            {{ textBox.text }}
          </div>
        </div>
      </div>

      <div class="properties-panel">
        <q-card>
          <q-card-section>
            <div class="text-h6">Properties</div>
          </q-card-section>
          <q-card-section v-if="selectedElementData" class="q-pt-none">
            <q-input
              v-model="selectedElementData.text"
              label="Text"
              outlined
              dense
              class="q-mb-md"
            />

            <div class="row q-col-gutter-sm q-mb-md">
              <div class="col-6">
                <q-input
                  v-model.number="selectedElementData.fontSize"
                  label="Font Size (px)"
                  type="number"
                  outlined
                  dense
                  min="8"
                  max="72"
                />
              </div>
              <div class="col-6">
                <q-select
                  v-model="selectedElementData.fontFamily"
                  :options="fontOptions"
                  label="Font Family"
                  outlined
                  dense
                />
              </div>
            </div>

            <div class="row q-col-gutter-sm q-mb-md">
              <div class="col-6">
                <q-input v-model="selectedElementData.color" label="Text Color" outlined dense>
                  <template v-slot:append>
                    <q-icon name="palette" class="cursor-pointer">
                      <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                        <q-color v-model="selectedElementData.color" />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
              <div class="col-6">
                <q-input
                  v-model="selectedElementData.backgroundColor"
                  label="Background Color"
                  outlined
                  dense
                >
                  <template v-slot:append>
                    <q-icon name="palette" class="cursor-pointer">
                      <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                        <q-color v-model="selectedElementData.backgroundColor" />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
            </div>

            <div class="row q-col-gutter-sm q-mb-md">
              <div class="col-3">
                <q-input
                  v-model.number="selectedElementData.x"
                  label="X"
                  type="number"
                  outlined
                  dense
                  min="0"
                  max="1920"
                />
              </div>
              <div class="col-3">
                <q-input
                  v-model.number="selectedElementData.y"
                  label="Y"
                  type="number"
                  outlined
                  dense
                  min="0"
                  max="1080"
                />
              </div>
              <div class="col-3">
                <q-input
                  v-model.number="selectedElementData.width"
                  label="Width"
                  type="number"
                  outlined
                  dense
                  min="10"
                  max="1920"
                />
              </div>
              <div class="col-3">
                <q-input
                  v-model.number="selectedElementData.height"
                  label="Height"
                  type="number"
                  outlined
                  dense
                  min="10"
                  max="1080"
                />
              </div>
            </div>

            <div class="row q-col-gutter-sm q-mb-md">
              <div class="col-6">
                <q-select
                  v-model="selectedElementData.textAlign"
                  :options="alignOptions"
                  label="Text Align"
                  outlined
                  dense
                />
              </div>
              <div class="col-6">
                <q-input
                  v-model.number="selectedElementData.opacity"
                  label="Opacity"
                  type="number"
                  outlined
                  dense
                  min="0"
                  max="1"
                  step="0.1"
                />
              </div>
            </div>

            <q-btn
              v-if="selectedElementData.type === 'textBox'"
              label="Delete Text Box"
              color="negative"
              outline
              @click="deleteTextBox"
              class="full-width"
            />
          </q-card-section>
          <q-card-section v-else class="text-center text-grey-6">
            Select an element to edit its properties
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Preview Dialog -->
    <q-dialog v-model="previewDialog" maximized>
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Overlay Preview</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        <q-card-section class="q-pt-none">
          <div class="preview-container">
            <iframe ref="previewFrame" class="preview-frame" :srcdoc="previewHTML"></iframe>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// Reactive data
const tileLabels = ref([])
const textBoxes = ref([])
const selectedElement = ref(null)
const previewDialog = ref(false)
const overlayCanvas = ref(null)

// Drag state
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// Options
const fontOptions = [
  'Arial',
  'Helvetica',
  'Times New Roman',
  'Courier New',
  'Verdana',
  'Georgia',
  'Palatino',
  'Garamond',
  'Bookman',
  'Trebuchet MS',
]

const alignOptions = ['left', 'center', 'right']

// Computed
const selectedElementData = computed(() => {
  if (!selectedElement.value) return null

  const tile = tileLabels.value.find((t) => t.id === selectedElement.value)
  if (tile) return { ...tile, type: 'tile' }

  const textBox = textBoxes.value.find((t) => t.id === selectedElement.value)
  if (textBox) return { ...textBox, type: 'textBox' }

  return null
})

const previewHTML = computed(() => {
  return generateHTML()
})

onMounted(() => {
  // Auto-import tiles on page load
  importTiles()

  // Add global mouse event listeners for dragging
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

// Functions
function importTiles() {
  const stored = localStorage.getItem('generatedAMCP')
  if (stored) {
    try {
      JSON.parse(stored) // Validate stored data
      tileLabels.value = []

      // Get tiles from main designer
      const designerTiles = JSON.parse(localStorage.getItem('designerTiles') || '[]')

      designerTiles.forEach((tile) => {
        const fx = tile.x / 1920
        const fy = tile.y / 1080
        const fw = tile.width / 1920

        tileLabels.value.push({
          id: `tile_${tile.id}`,
          text: tile.label,
          x: fx * 1920,
          y: fy * 1080,
          width: fw * 1920,
          height: 54, // Fixed height for labels
          fontSize: 14,
          fontFamily: 'Arial',
          color: '#ffffff',
          backgroundColor: 'rgba(0,0,0,0.7)',
          textAlign: 'center',
          opacity: 1,
        })
      })

      $q.notify({
        type: 'positive',
        message: `Imported ${tileLabels.value.length} tile labels`,
        position: 'top',
      })
    } catch {
      $q.notify({
        type: 'negative',
        message: 'Failed to import tiles',
        position: 'top',
      })
    }
  } else {
    $q.notify({
      type: 'warning',
      message: 'No tiles found. Create tiles in the designer first.',
      position: 'top',
    })
  }
}

function addTextBox() {
  const id = `textbox_${Date.now()}`
  textBoxes.value.push({
    id,
    text: 'New Text Box',
    x: 100,
    y: 100,
    width: 200,
    height: 50,
    fontSize: 16,
    fontFamily: 'Arial',
    color: '#ffffff',
    backgroundColor: 'rgba(0,0,0,0.8)',
    textAlign: 'center',
    opacity: 1,
  })
  selectedElement.value = id
}

function selectElement(id) {
  selectedElement.value = id
}

function deleteTextBox() {
  if (selectedElementData.value && selectedElementData.value.type === 'textBox') {
    textBoxes.value = textBoxes.value.filter((t) => t.id !== selectedElement.value)
    selectedElement.value = null
  }
}

function clearAll() {
  tileLabels.value = []
  textBoxes.value = []
  selectedElement.value = null
}

function getTileStyle(tile) {
  return {
    left: tile.x + 'px',
    top: tile.y + 'px',
    width: tile.width + 'px',
    height: tile.height + 'px',
    fontSize: tile.fontSize + 'px',
    fontFamily: tile.fontFamily,
    color: tile.color,
    backgroundColor: tile.backgroundColor,
    textAlign: tile.textAlign,
    opacity: tile.opacity,
    display: 'flex',
    alignItems: 'center',
    justifyContent:
      tile.textAlign === 'left' ? 'flex-start' : tile.textAlign === 'right' ? 'flex-end' : 'center',
    padding: '4px 8px',
  }
}

function getTextBoxStyle(textBox) {
  return getTileStyle(textBox)
}

function startDrag(event, elementId) {
  event.preventDefault()
  isDragging.value = true
  selectedElement.value = elementId

  const element = selectedElementData.value
  if (element) {
    dragOffset.value = {
      x: event.clientX - element.x,
      y: event.clientY - element.y,
    }
  }
}

function handleMouseMove(event) {
  if (!isDragging.value || !selectedElementData.value) return

  const canvasRect = overlayCanvas.value.getBoundingClientRect()
  const x = event.clientX - canvasRect.left - dragOffset.value.x
  const y = event.clientY - canvasRect.top - dragOffset.value.y

  // Constrain to canvas bounds
  const constrainedX = Math.max(0, Math.min(x, 1920 - selectedElementData.value.width))
  const constrainedY = Math.max(0, Math.min(y, 1080 - selectedElementData.value.height))

  // Update position
  if (selectedElementData.value.type === 'tile') {
    const tile = tileLabels.value.find((t) => t.id === selectedElement.value)
    if (tile) {
      tile.x = constrainedX
      tile.y = constrainedY
    }
  } else {
    const textBox = textBoxes.value.find((t) => t.id === selectedElement.value)
    if (textBox) {
      textBox.x = constrainedX
      textBox.y = constrainedY
    }
  }
}

function handleMouseUp() {
  isDragging.value = false
}

function previewOverlay() {
  previewDialog.value = true
}

function generateHTML() {
  const allElements = [...tileLabels.value, ...textBoxes.value]

  const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Multiviewer Overlay</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 1920px;
            height: 1080px;
            position: relative;
            background: transparent;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        .overlay-element {
            position: absolute;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
${allElements.map((element) => `    <div class="overlay-element" style="left: ${element.x}px; top: ${element.y}px; width: ${element.width}px; height: ${element.height}px; font-size: ${element.fontSize}px; font-family: ${element.fontFamily}; color: ${element.color}; background-color: ${element.backgroundColor}; text-align: ${element.textAlign}; opacity: ${element.opacity}; justify-content: ${element.textAlign === 'left' ? 'flex-start' : element.textAlign === 'right' ? 'flex-end' : 'center'};">${element.text}</div>`).join('\n')}
</body>
</html>`

  return htmlContent
}

async function exportHTML() {
  const html = generateHTML()
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  const blob = new Blob([html], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `multiviewer-overlay-${timestamp}.html`
  link.click()
  URL.revokeObjectURL(url)

  $q.notify({
    type: 'positive',
    message: 'HTML overlay exported successfully',
    position: 'top',
  })
}
</script>

<style scoped>
.overlay-editor-page {
  padding: 0;
  min-width: 1960px;
  overflow-x: auto;
}

.editor-container {
  display: flex;
  height: calc(100vh - 120px);
}

.canvas-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  background: #f5f5f5;
}

.overlay-canvas {
  width: 1920px;
  height: 1080px;
  background: #000;
  position: relative;
  border: 2px solid #333;
  overflow: hidden;
}

.overlay-element {
  position: absolute;
  cursor: move;
  user-select: none;
  border: 2px solid transparent;
  box-sizing: border-box;
}

.overlay-element.selected {
  border-color: #1976d2;
  box-shadow: 0 0 0 1px #1976d2;
}

.overlay-element:hover {
  border-color: #42a5f5;
}

.properties-panel {
  width: 350px;
  background: white;
  border-left: 1px solid #e0e0e0;
  overflow-y: auto;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  padding: 20px;
}

.preview-frame {
  width: 1920px;
  height: 1080px;
  border: none;
  transform: scale(0.5);
  transform-origin: top left;
}
</style>
