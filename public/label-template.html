<!doctype html>
<html>
  <head>
    <style>
      body {
        margin: 0;
        overflow: hidden;
      }
      #label {
        background: rgba(0, 0, 0, 0.6);
        color: white;
        font-family: sans-serif;
        font-size: 28px;
        padding: 6px 12px;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div id="label"><span id="text"></span></div>
    <script>
      function update(str) {
        const data = JSON.parse(str)
        document.getElementById('text').innerText = data.text || ''
      }
    </script>
  </body>
</html>
